"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { Search, LayoutDashboard, Calendar, ChevronDown } from "lucide-react";
import { UserMenu } from "@/components/user-menu";
import { useListColor } from "@/contexts/list-color-context";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { SkeletonNavigation } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { renderSpaceIcon } from "@/lib/space-icons";

// Custom N Icon component for Tasks
const NIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <Image
    src="/NeoTask_Icon_N.webp"
    alt="Tasks"
    width={24}
    height={24}
    className={`${className} object-contain`}
    style={style}
  />
);

const routes = [
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
  },
  {
    href: "/tasks",
    label: "Tasks",
    icon: NIcon,
  },
  {
    href: "/calendar",
    label: "Calendar",
    icon: Calendar,
  },
];

interface NavigationProps {
  isLoading?: boolean;
  showSkeleton?: boolean;
  currentSpace?: { id: string; name: string; icon?: string | null } | null;
  onSpaceClick?: () => void;
  isSpaceLoading?: boolean;
  useContainer?: boolean;
}

export function Navigation({
  isLoading = false,
  showSkeleton = false,
  currentSpace = null,
  onSpaceClick,
  isSpaceLoading = false,
  useContainer = true
}: NavigationProps = {}) {
  const [currentPath, setCurrentPath] = useState("");
  const [isScrollingDown, setIsScrollingDown] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [headerHeight, setHeaderHeight] = useState(48); // Default height (h-12)
  const pathname = usePathname();
  const { currentListColor, setCurrentListColor } = useListColor();
  const headerRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Only set the pathname on the client side
    setCurrentPath(pathname || "");
  }, [pathname]);

  // Get grey filter for inactive N icon to match other inactive tab icons
  const getInactiveNIconFilter = () => {
    // Convert white N icon to muted grey color similar to other inactive icons
    return "brightness(0) saturate(100%) invert(60%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(90%)";
  };

  // Measure header height on mount and resize
  useEffect(() => {
    const measureHeaderHeight = () => {
      if (headerRef.current) {
        const height = headerRef.current.offsetHeight;
        setHeaderHeight(height);
      }
    };

    measureHeaderHeight();
    window.addEventListener('resize', measureHeaderHeight);

    return () => {
      window.removeEventListener('resize', measureHeaderHeight);
    };
  }, []);

  // Scroll detection logic for hide/show behavior
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const currentScrollY = window.scrollY;
          const scrollDelta = currentScrollY - lastScrollY;

          // Enable on both mobile and desktop
          // (Removed mobile-only restriction)

          // Determine scroll direction with threshold to prevent jitter
          const scrollThreshold = 8;
          const minScrollY = 100; // Minimum scroll before hiding header

          if (Math.abs(scrollDelta) > scrollThreshold) {
            if (scrollDelta > 0 && currentScrollY > minScrollY) {
              // Scrolling down and past initial threshold
              setIsScrollingDown(true);
            } else if (scrollDelta < 0 || currentScrollY <= 50) {
              // Scrolling up or near top
              setIsScrollingDown(false);
            }
          }

          setLastScrollY(currentScrollY);
          ticking = false;
        });
        ticking = true;
      }
    };

    // Add scroll listener with passive option for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [lastScrollY, isScrollingDown]);

  // Reset list color context when navigating away from tasks page
  useEffect(() => {
    if (pathname && pathname !== "/tasks") {
      setCurrentListColor(null);
    }
  }, [pathname, setCurrentListColor]);

  // Find the current route to display on mobile
  const currentRoute = routes.find((route) => route.href === currentPath);

  // Get logo styles based on current list color
  const getLogoStyles = () => {
    if (!currentListColor) {
      // Plain white for colorless lists with subtle glow
      return {
        backgroundColor: '#ffffff',
        boxShadow: '0 0 8px rgba(255, 255, 255, 0.3), 0 0 16px rgba(255, 255, 255, 0.1)',
      };
    }

    // Apply solid color background based on current list color with subtle glow
    return {
      backgroundColor: currentListColor,
      boxShadow: `0 0 8px ${currentListColor}40, 0 0 16px ${currentListColor}20`,
    };
  };

  // Show skeleton loading state
  if (showSkeleton) {
    return (
      <header
        ref={headerRef}
        className="sticky top-0 z-50 w-full bg-background"
        style={{
          willChange: 'transform',
          transform: isScrollingDown ? `translateY(-${headerHeight}px)` : 'translateY(0)',
          transition: 'transform 350ms cubic-bezier(0.4, 0, 0.2, 1)',
        }}
      >
        <div className={useContainer ? "container-max-width mx-auto" : ""}>
          <SkeletonNavigation />
        </div>
      </header>
    );
  }

  return (
    <header
      ref={headerRef}
      className="sticky top-0 z-50 w-full bg-background"
      style={{
        willChange: 'transform',
        transform: isScrollingDown ? `translateY(-${headerHeight}px)` : 'translateY(0)',
        transition: 'transform 350ms cubic-bezier(0.4, 0, 0.2, 1)',
      }}
    >
      <div className={`${useContainer ? "container-max-width mx-auto" : ""} flex h-12 items-center justify-between px-3 relative`}>
        {/* App logo */}
        <div className="flex items-center">
          <Link href="/tasks" className="flex items-center space-x-2 p-2">
            <div
              className="relative w-16 h-16 md:w-20 md:h-20"
              style={{
                ...getLogoStyles(),
                WebkitMask: 'url(/NeoTask_Logo_white.webp) no-repeat center/contain',
                mask: 'url(/NeoTask_Logo_white.webp) no-repeat center/contain',
              }}
            />
          </Link>
          <nav className="hidden md:flex items-center ml-6 space-x-4 text-sm font-medium">
            {routes.map((route) => {
              const Icon = route.icon;
              const isActive = currentPath === route.href;
              const isTasksTab = route.href === "/tasks";

              return (
                <Link
                  key={route.href}
                  href={route.href}
                  className={`flex items-center justify-center w-8 h-8 rounded-lg transition-colors hover:!text-foreground hover:!bg-muted/50 ${
                    isActive
                      ? "text-foreground"
                      : "text-foreground/60"
                  }`}
                  title={route.label}
                >
                  {isTasksTab ? (
                    <Icon
                      className="h-5 w-5"
                      style={!isActive ? {
                        filter: getInactiveNIconFilter()
                      } : undefined}
                    />
                  ) : (
                    <Icon className="h-5 w-5" />
                  )}
                  <span className="sr-only">{route.label}</span>
                </Link>
              );
            })}
          </nav>
        </div>

        {/* Desktop Search Input - Absolutely centered on screen */}
        <div className="absolute left-0 right-0 mx-auto w-fit hidden md:block">
          <div className="relative max-w-md w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search tasks..."
              className="pl-9 h-8 bg-background/50 border-border/50 focus:bg-background focus:border-border w-80"
              autoComplete="off"
              spellCheck="false"
            />
          </div>
        </div>

        {/* Centered page title on mobile */}
        {currentRoute && (
          <div className="absolute left-0 right-0 mx-auto w-fit md:hidden">
            {currentRoute.href === "/tasks" ? (
              currentSpace ? (
                <button
                  type="button"
                  onClick={onSpaceClick}
                  className="space-name-glass flex items-center gap-1.5 text-sm font-medium px-2.5 py-1 rounded-lg group"
                >
                  {renderSpaceIcon(currentSpace.icon || "clipboard", "h-4 w-4 text-muted-foreground group-hover:text-white")}
                  <span className="text-muted-foreground group-hover:text-white">{currentSpace.name}</span>
                  {!isSpaceLoading && (
                    <ChevronDown className="h-3 w-3 text-muted-foreground group-hover:text-white flex-shrink-0" />
                  )}
                </button>
              ) : isSpaceLoading ? (
                <div className="space-name-glass flex items-center gap-1.5 px-2.5 py-1 rounded-lg animate-pulse">
                  <div className="h-4 w-4 bg-muted rounded"></div>
                  <div className="h-3 w-16 bg-muted rounded"></div>
                </div>
              ) : (
                <h1 className="font-medium">{currentRoute.label}</h1>
              )
            ) : (
              <h1 className="font-medium">{currentRoute.label}</h1>
            )}
          </div>
        )}

        {/* User menu always on the right */}
        <div className="flex items-center gap-4 z-10">


          {/* Mobile Search Icon */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden h-8 w-8 text-muted-foreground hover:text-foreground hover:!bg-muted/50 transition-colors rounded-lg"
          >
            <Search className="h-5 w-5 flex-shrink-0" />
            <span className="sr-only">Search</span>
          </Button>

          {isLoading ? (
            <div className="flex items-center gap-2">
              <LoadingSpinner size="sm" />
              <UserMenu />
            </div>
          ) : (
            <UserMenu />
          )}
        </div>
      </div>
    </header>
  );
}
